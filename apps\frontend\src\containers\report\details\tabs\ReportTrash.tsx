import { Text } from "@snap/design-system";
import { LuTrash2 } from "react-icons/lu";
import { ReportMetadata } from "~/types/global";
import { __PersonDetailsPage } from "root/modules/@snap/reports/ui/containers/report/";
import {
  useReportMetadata,
  useReportDetailActions,
  useReportSections,
  useReportType,
  useReportActionsWithAutoSave
} from "~/store/reportDetailStore";
import { useMemo } from "react";

export const ReportTrash = () => {
  const metadata = useReportMetadata();
  const reportType = useReportType();
  const { setMetadata, setReportSections, setReportType, updateSectionEntries } = useReportDetailActions();
  const allSections = useReportSections();
  const actions = useReportActionsWithAutoSave();

  const hasAnyDeleted = (obj: any): boolean => {
    if (obj && typeof obj === "object") {
      if (obj.is_deleted === true) return true;
      return Object.values(obj).some(hasAnyDeleted);
    }
    return false;
  }

  const trashSections = useMemo(
    () =>
      allSections.filter(sec => {
        if (sec.is_deleted === true || hasAnyDeleted(sec.data)) {
          return true;
        }

        // tratamento específico para a seção de Processos
        if (sec.title === "Processos" && sec.subsection) {
          const mainProcessosSection = allSections.find(
            (s) => s.title === "Processos" && !s.subsection
          );

          if (mainProcessosSection) {
            return sec.data.some((subsecEntry: any) => {
              const mainProcesso = mainProcessosSection.data.find(
                (p: any) => p.numero?.value === subsecEntry.numero?.value
              );
              return mainProcesso && hasAnyDeleted(mainProcesso);
            });
          }
        }

        return false;
      }),
    [allSections]
  );

  const renderContent = () => {
    if (!trashSections.length) {
      return (
        <div className="flex items-center justify-center min-h-[calc(100vh-300px)]">
          <div className="flex flex-col items-center gap-2 text-border py-8">
            <Text variant="label-lg" align="center"> A Lixeira está vazia</Text>
            <LuTrash2 size={56} />
          </div>
        </div>
      )
    }

    return (
      <div className="max-h-[calc(100vh-300px)] min-h-[calc(100vh-300px)] overflow-y-auto overflow-x-hidden">
        <__PersonDetailsPage
          store={{
            sections: trashSections,
            metadata: metadata as ReportMetadata,
            reportType: reportType,
            isTrashEnabled: true,
            isPrintEnabled: false,
            actions
          }}
          renderMode="trash"
        />
      </div>
    )
  }

  return renderContent()
}