import React, { useCallback } from "react";
import {
  useReportActions,
  useReportMode,
  useReportSections,
  useIsTrashEnabled,
  useIsPrintEnabled,
  ReportProvider
} from "../../context/ReportContext";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { translatePropToLabel, parseValue } from "../../helpers";
import {
  useReportSections as useStoreSections,
  useReportMetadata as useStoreMetadata,
  useReportType as useStoreReportType,
  useReportProfileImage as useStoreProfileImage,
  useReportActionsWithAutoSave
} from "~/store/reportDetailStore";
import { renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from '../components/accordion';
import { Badge } from '../components/badge';
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";
import {
  useModalControl,
  StandardList,
  ListItem,
  Button,
  ModalClose,
  Text
} from "@snap/design-system";
import { X } from 'lucide-react';
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";
import { Processo } from "../../model/Processo";

export function useRenderProcessos(
  sectionTitle: string
): ArrayRenderStrategy<Processo> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";
  const { open } = useModalControl();

  const sections = useReportSections();
  const isTrashEnabled = useIsTrashEnabled();
  const isPrintEnabled = useIsPrintEnabled();

  const mainSection = sections.find((s) => s.title === sectionTitle && !s.subsection);
  const subSections = sections.filter((s) => s.title === sectionTitle && !!s.subsection);


  const testFieldDeleted = (field: any): boolean => {
    return field?.is_deleted === true;
  };

  const testDetalhesDeleted = (detalhes: Record<string, any>): boolean => {
    if (!detalhes || Object.keys(detalhes).length === 0) return true;
    return Object.values(detalhes).every((field: any) => testFieldDeleted(field));
  };

  const testParticipantArrayDeleted = (participants: any[]): boolean => {
    if (!Array.isArray(participants) || participants.length === 0) return true;
    return participants.every((participant: any) => {
      if (participant.is_deleted) return true;
      if (participant.value && typeof participant.value === 'object') {
        return Object.values(participant.value).every((field: any) => testFieldDeleted(field));
      }
      return false;
    });
  };

  const testEntryDeleted = (entry: any): boolean => {
    if (entry.numero && !testFieldDeleted(entry.numero)) return false;

    if (entry.detalhes && !testDetalhesDeleted(entry.detalhes)) return false;

    if (entry.movimentações && !testFieldDeleted(entry.movimentações)) return false;
    const participantKeys = Object.keys(entry).filter(key =>
      key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
    );

    for (const key of participantKeys) {
      if (!testParticipantArrayDeleted(entry[key])) return false;
    }

    return true;
  };

  const testEntryHasDeletedFields = (entry: any): boolean => {
    if (entry.numero && testFieldDeleted(entry.numero)) return true;

    if (entry.detalhes && Object.values(entry.detalhes).some((d: any) => testFieldDeleted(d))) return true;

    if (entry.movimentações && testFieldDeleted(entry.movimentações)) return true;
    const participantKeys = Object.keys(entry).filter(key =>
      key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
    );

    for (const key of participantKeys) {
      const participants = entry[key] as any[];
      if (participants.some(p => p.is_deleted || (p.value && Object.values(p.value).some((f: any) => testFieldDeleted(f))))) {
        return true;
      }
    }

    return false;
  };

  const testSectionDeleted = (section: any): boolean => {
    if (!Array.isArray(section.data)) return true;

    if (!section.subsection) {
      return section.data.every((entry: any) => testEntryDeleted(entry));
    }

    return section.is_deleted === true;
  };

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;
    return section.data.reduce((count: number, entry: any) => {
      const isEntryDeleted = testEntryDeleted(entry);
      return isEntryDeleted ? count : count + 1;
    }, 0);
  };

  // Provider reativo necessário para usar no modal
  const ReactiveModalProvider = ({ numeroValue }: { numeroValue: string }) => {
    const freshSections = useStoreSections();
    const freshMetadata = useStoreMetadata();
    const freshReportType = useStoreReportType();
    const freshProfileImage = useStoreProfileImage();
    const freshActions = useReportActionsWithAutoSave();

    return (
      <ReportProvider
        sections={freshSections}
        metadata={freshMetadata!}
        reportType={freshReportType}
        isTrashEnabled={isTrashEnabled}
        isPrintEnabled={isPrintEnabled}
        image={freshProfileImage || undefined}
        actions={freshActions}
        renderMode={mode}
      >
        <ModalContentWithData
          numeroValue={numeroValue}
          mainSections={freshSections}
          mainMode={mode}
          mainActions={freshActions}
        />
      </ReportProvider>
    );
  };

  const ModalContentWithData = ({
    numeroValue,
    mainSections,
    mainMode,
    mainActions
  }: {
    numeroValue: string;
    mainSections: any[];
    mainMode: string;
    mainActions: any;
  }) => {
    const currentMainSection = mainSections.find((s: any) => s.title === sectionTitle && !s.subsection);
    const currentProcessoData = currentMainSection?.data.find((r: any) => r.numero.value === numeroValue);

    if (!currentProcessoData) {
      return <div>Dados não encontrados</div>;
    }





    const renderFreshProcessoModal = (processo: Processo) => {
      const elements: React.ReactElement[] = [];
      const processoIdx = currentMainSection?.data.findIndex((p: any) => p.numero.value === processo.numero.value) ?? -1;
      const currentProcesso = currentMainSection?.data[processoIdx] || processo;
      const modalIncludeKey = (isDeleted: boolean) => mainMode === "trash" ? isDeleted : !isDeleted;

      if (currentProcesso.numero && modalIncludeKey(currentProcesso.numero.is_deleted)) {
        elements.push(
          <CustomGridContainer cols={3} key="numero" respectColsInTrash={true}>
            <CustomGridItem
              cols={1}
              className="mb-6"
              onToggleField={() =>
                mainActions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === processoIdx && e.numero) {
                      e.numero.is_deleted = !e.numero.is_deleted;
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel(currentProcesso.numero.label || "numero").toUpperCase()}
                value={parseValue(String(currentProcesso.numero.value))}
                tooltip={renderSourceTooltip(currentProcesso.numero.source)}
              />
            </CustomGridItem>
          </CustomGridContainer>
        );
      }

      if (currentProcesso.detalhes && Object.keys(currentProcesso.detalhes).length > 0) {
        const detalhesEntries = Object.entries(currentProcesso.detalhes).filter(([_, val]) =>
          modalIncludeKey((val as any).is_deleted)
        );

        if (detalhesEntries.length > 0) {
          elements.push(
            <CustomGridContainer cols={2} key="detalhes" className="mb-6" respectColsInTrash={true}>
              {detalhesEntries.map(([fieldKey, val]) => (
                <CustomGridItem
                  key={fieldKey}
                  cols={1}
                  onToggleField={() =>
                    mainActions.updateSectionEntries!(
                      sectionTitle,
                      (e: any, i?: number) => {
                        if (i === processoIdx) {
                          const d = e.detalhes?.[fieldKey];
                          if (d) {
                            d.is_deleted = !d.is_deleted;
                          }
                        }
                      },
                      testEntryDeleted,
                      testSectionDeleted,
                      calculateDataCount
                    )
                  }
                >
                  <CustomReadOnlyInputField
                    label={translatePropToLabel((val as any).label || fieldKey).toUpperCase()}
                    value={parseValue(String((val as any).value))}
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                    tooltip={renderSourceTooltip((val as any).source)}
                  />
                </CustomGridItem>
              ))}
            </CustomGridContainer>
          );
        }
      }

      if (currentProcesso.movimentações && modalIncludeKey(currentProcesso.movimentações.is_deleted)) {
        elements.push(
          <CustomGridContainer cols={1} key="movimentacoes" className="mb-6" respectColsInTrash={true}>
            <CustomGridItem
              fullWidth
              onToggleField={() =>
                mainActions.updateSectionEntries!(
                  sectionTitle,
                  (e: any, i?: number) => {
                    if (i === processoIdx && e.movimentações) {
                      e.movimentações.is_deleted = !e.movimentações.is_deleted;
                    }
                  },
                  testEntryDeleted,
                  testSectionDeleted,
                  calculateDataCount
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel(currentProcesso.movimentações.label || "Movimentações").toUpperCase()}
                value={String(currentProcesso.movimentações.value)}
                element="textarea"
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip(currentProcesso.movimentações.source)}
              />
            </CustomGridItem>
          </CustomGridContainer>
        );
      }

      const participantKeys = Object.keys(currentProcesso).filter(key =>
        key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(currentProcesso[key])
      );

      participantKeys.forEach(key => {
        const participants = currentProcesso[key] as any[];

        // Lógica de visibilidade: lixeira mostra itens deletados ou com campos deletados
        const visibleParticipants = participants.filter(p => {
          if (mainMode === "trash") {
            const hasDeletedFields = p.value && Object.values(p.value).some((field: any) => field?.is_deleted === true);
            return p.is_deleted === true || hasDeletedFields;
          } else {
            const hasNonDeletedFields = p.value && Object.values(p.value).some((field: any) => field?.is_deleted === false);
            return p.is_deleted === false && hasNonDeletedFields;
          }
        });

        if (visibleParticipants.length > 0) {
          elements.push(
            <div key={key} className="mb-6">
              <CustomGridItem
                cols={1}
                containerClassName="w-fit pb-3 pr-12"
                onToggleField={() =>
                  mainActions.updateSectionEntries!(
                    sectionTitle,
                    (e: any, i?: number) => {
                      if (i === processoIdx && e[key]) {
                        const targetDeletedState = mainMode === "trash" ? false : true;
                        e[key].forEach((participant: any) => {
                          participant.is_deleted = targetDeletedState;
                          if (participant.value) {
                            Object.values(participant.value).forEach((campo: any) => {
                              if (campo) {
                                campo.is_deleted = targetDeletedState;
                              }
                            });
                          }
                        });
                      }
                    },
                    testEntryDeleted,
                    testSectionDeleted,
                    calculateDataCount
                  )
                }
              >
                <ReportsCustomLabel
                  label={translatePropToLabel(key).toUpperCase()}
                  colorClass="bg-primary"
                />
              </CustomGridItem>
              <CustomGridContainer cols={2} respectColsInTrash={true}>
                {visibleParticipants.map((participant, participantIdx) => {
                  const originalIdx = participants.indexOf(participant);
                  if (participant.value && typeof participant.value === 'object') {
                    return (
                      <div key={`${key}-${participantIdx}`} className="col-span-1">
                        <CustomGridItem
                          cols={1}
                          containerClassName="w-fit pr-12"
                          onToggleField={() =>
                            mainActions.updateSectionEntries!(
                              sectionTitle,
                              (e: any, i?: number) => {
                                if (i === processoIdx && e[key]?.[originalIdx]) {
                                  const targetDeletedState = mainMode === "trash" ? false : true;
                                  e[key][originalIdx].is_deleted = targetDeletedState;
                                  if (e[key][originalIdx].value) {
                                    Object.values(e[key][originalIdx].value).forEach((campo: any) => {
                                      if (campo) {
                                        campo.is_deleted = targetDeletedState;
                                      }
                                    });
                                  }
                                }
                              },
                              testEntryDeleted,
                              testSectionDeleted,
                              calculateDataCount
                            )
                          }
                        >
                          <ReportsCustomLabel
                            label={(() => {
                              const baseLabel = translatePropToLabel(key).toUpperCase();
                              if (mainMode === "trash") {
                                const isParticipantDeleted = participant.is_deleted === true;
                                return isParticipantDeleted ? `${baseLabel} ${participantIdx + 1}` : baseLabel;
                              } else {
                                return `${baseLabel} ${participantIdx + 1}`;
                              }
                            })()}
                            colorClass="bg-border"
                            icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                          />
                        </CustomGridItem>
                        <div className="pl-5">
                          {Object.entries(participant.value)
                            .filter(([_, field]) => modalIncludeKey((field as any)?.is_deleted))
                            .map(([fieldKey, field]) => (
                              <CustomGridItem
                                key={`${key}-${participantIdx}-${fieldKey}`}
                                cols={1}
                                className="py-1"
                                onToggleField={() =>
                                  mainActions.updateSectionEntries!(
                                    sectionTitle,
                                    (e: any, i?: number) => {
                                      if (i === processoIdx && e[key]?.[originalIdx]?.value?.[fieldKey]) {
                                        e[key][originalIdx].value[fieldKey].is_deleted = !e[key][originalIdx].value[fieldKey].is_deleted;

                                        const allFieldsDeleted = Object.values(e[key][originalIdx].value).every((f: any) => f.is_deleted === true);
                                        e[key][originalIdx].is_deleted = allFieldsDeleted;
                                      }
                                    },
                                    testEntryDeleted,
                                    testSectionDeleted,
                                    calculateDataCount
                                  )
                                }
                              >
                                <CustomReadOnlyInputField
                                  label={translatePropToLabel((field as any).label || fieldKey).toUpperCase()}
                                  colorClass="bg-border"
                                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                  value={parseValue(String((field as any).value))}
                                  tooltip={renderSourceTooltip((field as any).source)}
                                />
                              </CustomGridItem>
                            ))}
                        </div>
                      </div>
                    );
                  }
                  return null;
                })}
              </CustomGridContainer>
            </div>
          );
        }
      });

      return elements;
    };

    return (
      <div key={`modal-content-${numeroValue}-${mainMode}`}>
        {renderFreshProcessoModal(currentProcessoData as Processo).map((el, idx) => (
          <div key={idx} className="mb-4">
            {el}
          </div>
        ))}
      </div>
    );
  };

  const openProcessoDetails = useCallback((proc: Processo) => {
    const fullProcessoData = mainSection?.data.find((r: any) => r.numero.value === proc.numero.value);
    if (!fullProcessoData) return;

    const numeroValue = proc.numero.value;
    console.log('openProcessoDetails called for numeroValue:', numeroValue);
    console.log('openProcessoDetails - fullProcessoData:', fullProcessoData);

    open({
      modal: () => ({
        title: (
          <div className="flex items-center gap-2">
            <Text variant="label-lg" className="uppercase">Processo</Text>
            {isTrash && (
              <Badge variant="outline">
                <Text className="uppercase">lixeira</Text>
              </Badge>
            )}
          </div>
        ),
        icon: (
          <ModalClose>
            <X className="cursor-pointer" />
          </ModalClose>
        ),
        content: (
          <ReactiveModalProvider numeroValue={numeroValue} />
        ),
        footer: null,
      }),
      config: {
        content: { className: "max-w-5xl" },
        contentWrapper: {
          className: "max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable]"
        }
      },
    });
  }, [open, mainSection]);

  const deleteSubsectionEntries = useCallback((subsectionName: string) => {
    const updater = actions.updateSubsectionWithMainSection;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      subsectionName,
      'numero',
      entry => {
        if (entry.numero) {
          entry.numero.is_deleted = targetDeletedState;
        }

        if (entry.detalhes) {
          Object.keys(entry.detalhes).forEach(key => {
            if (entry.detalhes[key]) {
              entry.detalhes[key].is_deleted = targetDeletedState;
            }
          });
        }

        if (entry.movimentações) {
          entry.movimentações.is_deleted = targetDeletedState;
        }

        const participantKeys = Object.keys(entry).filter(key =>
          key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
        );

        participantKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((participant: any) => {
              participant.is_deleted = targetDeletedState;
              if (participant.value && typeof participant.value === 'object') {
                Object.keys(participant.value).forEach(fieldKey => {
                  if (participant.value[fieldKey]) {
                    participant.value[fieldKey].is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  }, [actions, mode, sectionTitle]);

  const deleteIndividualProcesso = useCallback((numeroValue: string) => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.numero?.value === numeroValue) {
          if (entry.numero) {
            entry.numero.is_deleted = targetDeletedState;
          }
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount,
      true,
      {
        matchingProp: 'numero',
        updaterFn: entry => {
          if (entry.numero?.value === numeroValue) {
            if (entry.numero) {
              entry.numero.is_deleted = targetDeletedState;
            }
            if (entry.detalhes) {
              Object.keys(entry.detalhes).forEach(key => {
                if (entry.detalhes[key]) {
                  entry.detalhes[key].is_deleted = targetDeletedState;
                }
              });
            }
            if (entry.movimentações) {
              entry.movimentações.is_deleted = targetDeletedState;
            }
            const participantKeys = Object.keys(entry).filter(key =>
              key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
            );
            participantKeys.forEach(key => {
              if (Array.isArray(entry[key])) {
                entry[key].forEach((participant: any) => {
                  participant.is_deleted = targetDeletedState;
                  if (participant.value && typeof participant.value === 'object') {
                    Object.keys(participant.value).forEach(fieldKey => {
                      if (participant.value[fieldKey]) {
                        participant.value[fieldKey].is_deleted = targetDeletedState;
                      }
                    });
                  }
                });
              }
            });
          }
        }
      }
    );
  }, [actions, mode, sectionTitle]);

  const validateKeys = (keys: Array<keyof Processo>): boolean => {
    return keys.some((campo) => campo === 'numero' || campo === 'detalhes' || campo === 'movimentações');
  };

  const formatByKey: Record<string, any> = {};

  const render = (): React.ReactElement[] => {
    if (!mainSection) {
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    if (!subSections.length) {
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    const filteredSubSections = subSections.filter(sec => {

      if (isTrash) {
        const shouldShow = sec.is_deleted || sec.data.some(proc => {
          const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
          const hasDeletedFields = fullProcesso && testEntryHasDeletedFields(fullProcesso);
          return hasDeletedFields;
        });
        return shouldShow;
      } else {
        const shouldShow = !sec.is_deleted && sec.data.some(proc => {
          const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
          const isNotCompletelyDeleted = fullProcesso && !testEntryDeleted(fullProcesso);
          return isNotCompletelyDeleted;
        });
        return shouldShow;
      }
    });

    if (filteredSubSections.length === 0) {
      if (isTrash && mainSection.data.some(proc => testEntryHasDeletedFields(proc))) {
        return [<div key="no-data">Processos deletados encontrados, mas nenhuma subseção visível</div>];
      }
      return [<div key="no-data">Nenhum dado encontrado</div>];
    }

    return [
      <Accordion type="multiple" key="processos-accordion">
        {filteredSubSections.map((sec, i) => {
          const filteredProcessos = sec.data.filter(proc => {
            const fullProcesso = mainSection.data.find(p => p.numero.value === proc.numero.value);
            if (!fullProcesso) return false;

            if (isTrash) {
              return testEntryHasDeletedFields(fullProcesso);
            } else {
              return !testEntryDeleted(fullProcesso);
            }
          });

          const dataCount = filteredProcessos.length;

          return (
            <AccordionItem key={`${sec.subsection}-${i}`} value={sec.subsection} className='border-b-neutral-100'>
              <div className="group">
                <AccordionTrigger className="px-0 py-0 mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer">
                  <div className="flex items-center gap-4 w-full">
                    <h3 className="font-mono text-lg uppercase">{translatePropToLabel(sec.subsection)}</h3>
                    <div className="flex items-center gap-2">
                      {!isTrash && (
                        <Badge variant="secondary" className="rounded-2xl px-4 py-0.5 bg-gray-400">
                          {dataCount}
                        </Badge>
                      )}
                      {isTrashEnabled && (
                        <span
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteSubsectionEntries(sec.subsection);
                          }}
                          title={isTrash ? "Restaurar subseção" : "Deletar subseção"}
                          className={`
                          flex items-center justify-center
                          w-8 h-8
                          opacity-0
                          group-hover:opacity-100
                          cursor-pointer
                          transition-opacity duration-200
                        `}
                        >
                          {isTrash ? (
                            <LiaTrashRestoreAltSolid
                              size={32}
                              color="var(--foreground)"
                            />
                          ) : (
                            <LiaTrashAltSolid
                              size={32}
                              color="var(--primary)"
                            />
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
              </div>
              <AccordionContent className="px-8 py-4 border-t">
                <StandardList withSeparator>
                  {filteredProcessos.map(proc => (
                    <ListItem key={proc.numero.value} className={`w-full flex justify-between gap-2 ${isTrash ? 'flex-col items-start' : ''} group`}>
                      <div className="flex items-center gap-2">
                        <ReportsCustomLabel
                          label={`Número: ${proc.numero.value}`}
                          colorClass="bg-primary"
                        />
                        {
                          isTrashEnabled && (
                            <span
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteIndividualProcesso(proc.numero.value);
                              }}
                              title={isTrash ? "Restaurar processo" : "Deletar processo"}
                              className={`
                            flex items-center justify-center
                            w-6 h-6
                            opacity-0
                            group-hover:opacity-100
                            cursor-pointer
                            transition-opacity duration-200
                          `}
                            >
                              {isTrash ? (
                                <LiaTrashRestoreAltSolid
                                  size={24}
                                  color="var(--foreground)"
                                />
                              ) : (
                                <LiaTrashAltSolid
                                  size={24}
                                  color="var(--primary)"
                                />
                              )}
                            </span>
                          )
                        }
                      </div>
                      <Button onClick={() => openProcessoDetails(proc as Processo)}>
                        Ver detalhes
                      </Button>
                    </ListItem>
                  ))}
                </StandardList>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    ];
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        if (entry.numero) {
          entry.numero.is_deleted = targetDeletedState;
        }

        if (entry.detalhes) {
          Object.keys(entry.detalhes).forEach(key => {
            if (entry.detalhes[key]) {
              entry.detalhes[key].is_deleted = targetDeletedState;
            }
          });
        }

        if (entry.movimentações) {
          entry.movimentações.is_deleted = targetDeletedState;
        }

        const participantKeys = Object.keys(entry).filter(key =>
          key !== 'numero' && key !== 'detalhes' && key !== 'movimentações' && Array.isArray(entry[key])
        );

        participantKeys.forEach(key => {
          if (Array.isArray(entry[key])) {
            entry[key].forEach((participant: any) => {
              participant.is_deleted = targetDeletedState;
              if (participant.value && typeof participant.value === 'object') {
                Object.keys(participant.value).forEach(fieldKey => {
                  if (participant.value[fieldKey]) {
                    participant.value[fieldKey].is_deleted = targetDeletedState;
                  }
                });
              }
            });
          }
        });
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount,
      true
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
